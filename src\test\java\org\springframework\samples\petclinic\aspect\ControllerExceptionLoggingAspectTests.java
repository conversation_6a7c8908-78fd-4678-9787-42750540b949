/*
 * Copyright 2012-2019 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.samples.petclinic.aspect;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 测试Controller异常日志切面功能
 *
 * <AUTHOR>
 */
@SpringBootTest
@AutoConfigureWebMvc
@TestPropertySource(properties = { "server.error.include-message=ALWAYS" })
class ControllerExceptionLoggingAspectTests {

	@Autowired
	private WebApplicationContext webApplicationContext;

	@Test
	void testControllerExceptionLogging() throws Exception {
		MockMvc mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

		// 删除现有的error.log文件（如果存在）
		File errorLogFile = new File("error.log");
		if (errorLogFile.exists()) {
			errorLogFile.delete();
		}

		// 触发异常
		mockMvc.perform(get("/oups")).andExpect(status().isInternalServerError());

		// 等待一下让日志写入
		Thread.sleep(1000);

		// 验证error.log文件是否被创建
		assertThat(errorLogFile).exists();

		// 验证日志内容
		String logContent = Files.readString(Paths.get("error.log"));
		assertThat(logContent).contains("Controller异常");
		assertThat(logContent).contains("CrashController");
		assertThat(logContent).contains("triggerException");
		assertThat(logContent).contains("RuntimeException");
		assertThat(logContent).contains("Expected: controller used to showcase what happens when an exception is thrown");
	}

}
