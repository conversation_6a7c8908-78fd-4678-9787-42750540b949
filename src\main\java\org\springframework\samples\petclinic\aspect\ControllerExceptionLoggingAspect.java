/*
 * Copyright 2012-2019 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springframework.samples.petclinic.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Arrays;

/**
 * 切面类，用于拦截Controller层的异常并记录到专门的日志文件中
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class ControllerExceptionLoggingAspect {

	// 专门用于记录Controller异常的logger，对应logback配置中的特定logger
	private static final Logger controllerExceptionLogger = LoggerFactory
		.getLogger("org.springframework.samples.petclinic.aspect.ControllerExceptionLogger");

	// 普通的应用日志logger
	private static final Logger logger = LoggerFactory.getLogger(ControllerExceptionLoggingAspect.class);

	/**
	 * 环绕通知，拦截所有Controller类中的方法
	 * 切点表达式：拦截org.springframework.samples.petclinic包及其子包下所有标注了@Controller的类的所有方法
	 */
	@Around("execution(* org.springframework.samples.petclinic..*.*(..)) && @within(org.springframework.stereotype.Controller)")
	public Object logControllerExceptions(ProceedingJoinPoint joinPoint) throws Throwable {
		String methodName = joinPoint.getSignature().toShortString();
		String className = joinPoint.getTarget().getClass().getSimpleName();

		try {
			// 执行原方法
			Object result = joinPoint.proceed();
			return result;
		}
		catch (Exception e) {
			// 获取请求信息
			String requestInfo = getRequestInfo();
			String methodArgs = getMethodArguments(joinPoint);

			// 记录异常信息到专门的logger（会同时输出到控制台和error.log文件）
			controllerExceptionLogger.error("Controller异常 - 类: {}, 方法: {}, 请求信息: {}, 方法参数: {}, 异常类型: {}, 异常消息: {}",
					className, methodName, requestInfo, methodArgs, e.getClass().getSimpleName(), e.getMessage(), e);

			// 记录到普通日志（只输出到控制台）
			logger.info("Controller异常已记录到error.log文件 - 类: {}, 方法: {}", className, methodName);

			// 重新抛出异常，保持原有的异常处理流程
			throw e;
		}
	}

	/**
	 * 获取HTTP请求信息
	 */
	private String getRequestInfo() {
		try {
			ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder
				.getRequestAttributes();
			if (attributes != null) {
				HttpServletRequest request = attributes.getRequest();
				return String.format("Method: %s, URI: %s, RemoteAddr: %s", request.getMethod(),
						request.getRequestURI(), request.getRemoteAddr());
			}
		}
		catch (Exception e) {
			logger.debug("无法获取请求信息: {}", e.getMessage());
		}
		return "N/A";
	}

	/**
	 * 获取方法参数信息（简化版，避免敏感信息泄露）
	 */
	private String getMethodArguments(ProceedingJoinPoint joinPoint) {
		try {
			Object[] args = joinPoint.getArgs();
			if (args == null || args.length == 0) {
				return "无参数";
			}

			// 简化参数信息，只记录参数类型和数量，避免记录敏感数据
			StringBuilder sb = new StringBuilder();
			for (int i = 0; i < args.length; i++) {
				if (i > 0) {
					sb.append(", ");
				}
				Object arg = args[i];
				if (arg != null) {
					sb.append(arg.getClass().getSimpleName());
				}
				else {
					sb.append("null");
				}
			}
			return sb.toString();
		}
		catch (Exception e) {
			logger.debug("无法获取方法参数信息: {}", e.getMessage());
			return "参数信息获取失败";
		}
	}

}
