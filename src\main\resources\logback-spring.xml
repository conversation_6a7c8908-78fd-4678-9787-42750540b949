<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 定义日志输出格式 -->
    <property name="CONSOLE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"/>
    <property name="FILE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"/>

    <!-- 控制台输出appender - 所有日志都输出到标准输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 文件输出appender - 专门用于Controller异常日志 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>error.log</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按天滚动 -->
            <fileNamePattern>error.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 保留30天的日志文件 -->
            <maxHistory>30</maxHistory>
            <!-- 总大小限制为1GB -->
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <!-- 只记录ERROR级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 专门用于Controller异常的logger -->
    <logger name="org.springframework.samples.petclinic.aspect.ControllerExceptionLogger" level="ERROR" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ERROR_FILE"/>
    </logger>

    <!-- 根logger配置 - 所有其他日志只输出到控制台 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>

    <!-- Spring框架日志级别配置 -->
    <logger name="org.springframework" level="INFO"/>
    <logger name="org.springframework.web" level="INFO"/>
    <logger name="org.springframework.context.annotation" level="INFO"/>
    
    <!-- 应用程序日志级别配置 -->
    <logger name="org.springframework.samples.petclinic" level="INFO"/>
    
    <!-- Hibernate/JPA日志级别配置 -->
    <logger name="org.hibernate" level="WARN"/>
    <logger name="org.hibernate.SQL" level="DEBUG"/>
    <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="TRACE"/>
</configuration>
